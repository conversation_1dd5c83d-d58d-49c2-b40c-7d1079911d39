services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ecom_postgres
    environment:
      POSTGRES_DB: ecommerce_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ecom_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ecom_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ecom_network

  # E-commerce API
  api:
    build: .
    container_name: ecom_api
    ports:
      - "8080:8080"
    environment:
      - APP_ENV=development
      - APP_HOST=0.0.0.0
      - APP_PORT=8080
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=password
      - DB_NAME=ecommerce_db
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your-super-secret-jwt-key
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GOOGLE_REDIRECT_URL=http://localhost:8080/api/v1/auth/google/callback
      - FACEBOOK_APP_ID=${FACEBOOK_APP_ID}
      - FACEBOOK_APP_SECRET=${FACEBOOK_APP_SECRET}
      - FACEBOOK_REDIRECT_URL=http://localhost:8080/api/v1/auth/facebook/callback
      - CORS_ALLOWED_METHODS=GET,POST,PUT,PATCH,DELETE,OPTIONS
    # volumes:
      # - ./uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - ecom_network
    restart: unless-stopped

  # pgAdmin (optional - for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ecom_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - ecom_network
    profiles:
      - tools

volumes:
  postgres_data:
  redis_data:
  uploads_data:

networks:
  ecom_network:
    driver: bridge
