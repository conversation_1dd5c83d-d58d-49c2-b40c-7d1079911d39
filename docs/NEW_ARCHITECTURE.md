# New Clean Architecture Structure

## Overview

The project has been refactored to follow Domain-Driven Design (DDD) principles combined with Clean Architecture. This new structure organizes code by business domains rather than technical layers, making it easier to find, maintain, and scale.

## Directory Structure

```
internal/
├── shared/                          # Shared components across domains
│   ├── entities/                    # Common entities (BaseEntity, pagination, errors)
│   ├── interfaces/                  # Common interfaces (repository patterns)
│   └── utils/                       # Shared utilities (response helpers)
├── domains/                         # Business domains
│   ├── user/                        # User Management Domain
│   ├── product/                     # Product Catalog Domain
│   ├── inventory/                   # Inventory Management Domain
│   ├── shopping/                    # Shopping Experience Domain (cart, wishlist)
│   ├── order/                       # Order Management Domain
│   ├── payment/                     # Payment Processing Domain
│   ├── content/                     # Content Management Domain (files, images)
│   ├── analytics/                   # Analytics & Reporting Domain
│   ├── notification/                # Notification Domain
│   └── address/                     # Address Management Domain
├── infrastructure/                  # Infrastructure Layer
│   ├── database/
│   │   └── repositories/            # Database implementations
│   ├── cache/
│   ├── storage/
│   ├── services/
│   └── config/
└── delivery/                        # Main delivery layer
    ├── http/
    │   ├── middleware/
    │   ├── routes/
    │   └── server.go
    └── grpc/                        # Future gRPC support
```

## Domain Structure

Each domain follows the same internal structure:

```
domain_name/
├── entities/                        # Domain entities
├── repositories/                    # Repository interfaces
├── usecases/                        # Business logic
│   └── interfaces/                  # Use case interfaces
└── delivery/                        # Delivery layer
    └── http/
        ├── handlers/                # HTTP handlers
        └── routes/                  # Route definitions
```

## Benefits

### 1. **Easy Navigation**
- Want to work with users? → `internal/domains/user/`
- Want to work with products? → `internal/domains/product/`
- All related code is in one place

### 2. **Clear Separation of Concerns**
- Each domain is independent
- Shared components are clearly separated
- Infrastructure is isolated

### 3. **Scalability**
- Easy to add new domains
- Can be split into microservices later
- Team can work on different domains independently

### 4. **Maintainability**
- Clear dependencies
- Easy to test individual domains
- Reduced coupling between domains

## How to Find Things

### Working with a specific feature:
1. **User authentication/management** → `internal/domains/user/`
2. **Product catalog** → `internal/domains/product/`
3. **Shopping cart/wishlist** → `internal/domains/shopping/`
4. **Orders and shipping** → `internal/domains/order/`
5. **Payments and coupons** → `internal/domains/payment/`
6. **File uploads** → `internal/domains/content/`
7. **Analytics and reports** → `internal/domains/analytics/`
8. **Notifications** → `internal/domains/notification/`
9. **Address management** → `internal/domains/address/`
10. **Inventory management** → `internal/domains/inventory/`

### Working with infrastructure:
- **Database connections** → `internal/infrastructure/database/`
- **Cache (Redis)** → `internal/infrastructure/cache/`
- **File storage** → `internal/infrastructure/storage/`
- **External services** → `internal/infrastructure/services/`
- **Configuration** → `internal/infrastructure/config/`

### Working with HTTP layer:
- **Middleware** → `internal/delivery/http/middleware/`
- **Main routes** → `internal/delivery/http/routes/`
- **Domain-specific routes** → `internal/domains/{domain}/delivery/http/routes/`

## Migration Status

### ✅ Completed:
- [x] Created shared components structure
- [x] Migrated User domain
- [x] Migrated Product domain  
- [x] Migrated Order domain
- [x] Migrated Shopping domain (cart, wishlist)
- [x] Migrated Payment domain
- [x] Migrated Inventory domain
- [x] Migrated Content domain (file management)
- [x] Migrated Analytics domain
- [x] Migrated Notification domain
- [x] Migrated Address domain

### 🔄 In Progress:
- [ ] Update import paths throughout codebase
- [ ] Test and validate functionality
- [ ] Update documentation

### 📋 Next Steps:
1. Update all import statements to use new paths
2. Run tests to ensure everything works
3. Update API documentation
4. Create domain-specific documentation

## Development Guidelines

### Adding a new feature:
1. Identify which domain it belongs to
2. Add entities to `{domain}/entities/`
3. Add repository interfaces to `{domain}/usecases/interfaces/`
4. Add use cases to `{domain}/usecases/`
5. Add handlers to `{domain}/delivery/http/handlers/`
6. Add routes to `{domain}/delivery/http/routes/`
7. Add database implementation to `internal/infrastructure/database/repositories/`

### Cross-domain communication:
- Use dependency injection through interfaces
- Avoid direct imports between domains
- Use shared entities for common data structures
- Communicate through use case interfaces

## File Naming Conventions

- **Entities**: `{entity_name}.go` (e.g., `user.go`, `product.go`)
- **Repositories**: `{entity_name}_repository.go`
- **Use Cases**: `{entity_name}_usecase.go`
- **Handlers**: `{entity_name}_handler.go`
- **Routes**: `{domain_name}_routes.go`

This new structure makes the codebase much more organized and easier to navigate as it grows!
