/* ===== UNIFIED DESIGN SYSTEM FOR SHOPHUB ===== */

:root {
  /* ===== PRIMARY BRAND COLORS ===== */
  --color-primary-50: #FFF8F0;
  --color-primary-100: #FFEFDB;
  --color-primary-200: #FFDFB7;
  --color-primary-300: #FFCF93;
  --color-primary-400: #FFBF6F;
  --color-primary-500: #FF9000;  /* Main brand orange */
  --color-primary-600: #E6820E;
  --color-primary-700: #CC7300;
  --color-primary-800: #B26400;
  --color-primary-900: #995500;
  --color-primary-950: #663800;

  /* ===== NEUTRAL COLORS ===== */
  --color-neutral-0: #FFFFFF;
  --color-neutral-50: #F8FAFC;
  --color-neutral-100: #F1F5F9;
  --color-neutral-200: #E2E8F0;
  --color-neutral-300: #CBD5E1;
  --color-neutral-400: #94A3B8;
  --color-neutral-500: #64748B;
  --color-neutral-600: #475569;
  --color-neutral-700: #334155;
  --color-neutral-800: #1E293B;
  --color-neutral-900: #0F172A;
  --color-neutral-950: #000000;

  /* ===== SEMANTIC COLORS ===== */
  --color-success-50: #F0FDF4;
  --color-success-500: #22C55E;
  --color-success-600: #16A34A;
  --color-success-900: #14532D;

  --color-warning-50: #FFFBEB;
  --color-warning-500: #F59E0B;
  --color-warning-600: #D97706;
  --color-warning-900: #92400E;

  --color-error-50: #FEF2F2;
  --color-error-500: #EF4444;
  --color-error-600: #DC2626;
  --color-error-900: #7F1D1D;

  --color-info-50: #EFF6FF;
  --color-info-500: #3B82F6;
  --color-info-600: #2563EB;
  --color-info-900: #1E3A8A;

  /* ===== THEME TOKENS (DARK THEME DEFAULT) ===== */
  --background: var(--color-neutral-950);
  --foreground: var(--color-neutral-0);
  --muted: var(--color-neutral-800);
  --muted-foreground: var(--color-neutral-400);
  --popover: var(--color-neutral-0);
  --popover-foreground: var(--color-neutral-950);
  --card: var(--color-neutral-0);
  --card-foreground: var(--color-neutral-950);
  --border: var(--color-neutral-700);
  --input: var(--color-neutral-700);
  --primary: var(--color-primary-500);
  --primary-foreground: var(--color-neutral-0);
  --secondary: var(--color-neutral-800);
  --secondary-foreground: var(--color-neutral-0);
  --accent: var(--color-primary-500);
  --accent-foreground: var(--color-neutral-0);
  --destructive: var(--color-error-500);
  --destructive-foreground: var(--color-neutral-0);
  --ring: var(--color-primary-500);
  --radius: 0.75rem;

  /* ===== SPACING SCALE ===== */
  --space-0: 0;
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
  --space-32: 8rem;     /* 128px */

  /* Typography Scale */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */
  --text-5xl: 3rem;       /* 48px */
  --text-6xl: 3.75rem;    /* 60px */
  --text-7xl: 4.5rem;     /* 72px */
  --text-8xl: 6rem;       /* 96px */

  /* Line Heights */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Font Weights */
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* Border Radius */
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

  /* Z-Index Scale */
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-auto: auto;

  /* Transition Durations */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;

  /* Transition Timing Functions */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* Component Specific Tokens */
  --button-height-sm: 2.25rem;    /* 36px */
  --button-height-md: 2.5rem;     /* 40px */
  --button-height-lg: 3rem;       /* 48px */
  --button-height-xl: 3.5rem;     /* 56px */

  --input-height-sm: 2.25rem;     /* 36px */
  --input-height-md: 2.75rem;     /* 44px */
  --input-height-lg: 3.25rem;     /* 52px */

  --card-padding-sm: var(--space-4);
  --card-padding-md: var(--space-6);
  --card-padding-lg: var(--space-8);

  /* Animation Keyframes */
  --animate-spin: spin 1s linear infinite;
  --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
  --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animate-bounce: bounce 1s infinite;
}

/* ===== LIGHT THEME OVERRIDES ===== */
.light {
  --background: var(--color-neutral-0);
  --foreground: var(--color-neutral-950);
  --muted: var(--color-neutral-100);
  --muted-foreground: var(--color-neutral-500);
  --popover: var(--color-neutral-0);
  --popover-foreground: var(--color-neutral-950);
  --card: var(--color-neutral-0);
  --card-foreground: var(--color-neutral-950);
  --border: var(--color-neutral-200);
  --input: var(--color-neutral-200);
  --secondary: var(--color-neutral-100);
  --secondary-foreground: var(--color-neutral-950);
  --accent: var(--color-primary-500);
  --accent-foreground: var(--color-neutral-0);
}

/* ===== GLOBAL BASE STYLES ===== */
* {
  border-color: var(--border);
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-feature-settings: "rlig" 1, "calt" 1;
  transition: background-color var(--duration-200) var(--ease-out), color var(--duration-200) var(--ease-out);
}

/* ===== TYPOGRAPHY STYLES ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-semibold);
  letter-spacing: -0.025em;
  color: var(--foreground);
}

h1 {
  font-size: var(--text-4xl);
  line-height: var(--leading-tight);
}

h2 {
  font-size: var(--text-3xl);
  line-height: var(--leading-tight);
}

h3 {
  font-size: var(--text-2xl);
  line-height: var(--leading-snug);
}

h4 {
  font-size: var(--text-xl);
  line-height: var(--leading-snug);
}

@media (min-width: 1024px) {
  h1 {
    font-size: var(--text-5xl);
    line-height: var(--leading-tight);
  }

  h2 {
    font-size: var(--text-4xl);
    line-height: var(--leading-tight);
  }

  h3 {
    font-size: var(--text-3xl);
    line-height: var(--leading-snug);
  }

  h4 {
    font-size: var(--text-2xl);
    line-height: var(--leading-snug);
  }
}

/* Utility Classes */
.container-padding {
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

@media (min-width: 640px) {
  .container-padding {
    padding-left: var(--space-6);
    padding-right: var(--space-6);
  }
}

@media (min-width: 1024px) {
  .container-padding {
    padding-left: var(--space-8);
    padding-right: var(--space-8);
  }
}

/* Section Spacing */
.section-padding-sm {
  padding-top: var(--space-12);
  padding-bottom: var(--space-12);
}

.section-padding-md {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}

.section-padding-lg {
  padding-top: var(--space-24);
  padding-bottom: var(--space-24);
}

/* Typography Utilities */
.heading-1 {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
}

.heading-2 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
}

.heading-3 {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  line-height: var(--leading-snug);
}

.body-large {
  font-size: var(--text-lg);
  line-height: var(--leading-relaxed);
}

.body-base {
  font-size: var(--text-base);
  line-height: var(--leading-normal);
}

.body-small {
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
}

/* Interactive States */
.interactive-scale {
  transition: transform var(--duration-200) var(--ease-out);
}

.interactive-scale:hover {
  transform: scale(1.05);
}

.interactive-scale:active {
  transform: scale(0.95);
}

/* Focus States */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Glass Effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-600)) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Card Variants */
.card-elevated {
  box-shadow: var(--shadow-lg);
  transition: box-shadow var(--duration-300) var(--ease-out);
}

.card-elevated:hover {
  box-shadow: var(--shadow-2xl);
}

/* ===== UTILITY CLASSES ===== */
.text-gradient {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
}

.shadow-primary {
  box-shadow: 0 10px 15px -3px rgb(255 144 0 / 0.25), 0 4px 6px -4px rgb(255 144 0 / 0.1);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* ===== COMPONENT OVERRIDES ===== */
.btn-primary {
  background-color: var(--primary);
  color: var(--primary-foreground);
  border: none;
  transition: all var(--duration-200) var(--ease-out);
}

.btn-primary:hover {
  background-color: var(--color-primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-primary);
}

.card {
  background-color: var(--card);
  color: var(--card-foreground);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  transition: all var(--duration-200) var(--ease-out);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* ===== SCROLLBAR STYLING ===== */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-600);
}

/* ===== SELECTION STYLING ===== */
::selection {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

::-moz-selection {
  background-color: var(--primary);
  color: var(--primary-foreground);
}
