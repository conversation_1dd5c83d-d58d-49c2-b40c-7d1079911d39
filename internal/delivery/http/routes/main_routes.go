package routes

import (
	"github.com/gin-gonic/gin"
	
	// Domain route imports
	userRoutes "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/user/delivery/http/routes"
	productRoutes "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/product/delivery/http/routes"
	orderRoutes "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/order/delivery/http/routes"
	shoppingRoutes "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/shopping/delivery/http/routes"
	paymentRoutes "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/payment/delivery/http/routes"
	inventoryRoutes "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/inventory/delivery/http/routes"
	contentRoutes "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/content/delivery/http/routes"
	analyticsRoutes "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/analytics/delivery/http/routes"
	notificationRoutes "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/notification/delivery/http/routes"
	addressRoutes "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/address/delivery/http/routes"
	
	// Handler imports
	userHandlers "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/user/delivery/http/handlers"
	productHandlers "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/product/delivery/http/handlers"
	orderHandlers "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/order/delivery/http/handlers"
	shoppingHandlers "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/shopping/delivery/http/handlers"
	paymentHandlers "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/payment/delivery/http/handlers"
	inventoryHandlers "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/inventory/delivery/http/handlers"
	contentHandlers "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/content/delivery/http/handlers"
	analyticsHandlers "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/analytics/delivery/http/handlers"
	notificationHandlers "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/notification/delivery/http/handlers"
	addressHandlers "github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/address/delivery/http/handlers"
)

// DomainHandlers contains all domain handlers
type DomainHandlers struct {
	// User domain handlers
	UserHandler  *userHandlers.UserHandler
	AdminHandler *userHandlers.AdminHandler
	OAuthHandler *userHandlers.OAuthHandler
	
	// Product domain handlers
	ProductHandler  *productHandlers.ProductHandler
	CategoryHandler *productHandlers.CategoryHandler
	ReviewHandler   *productHandlers.ReviewHandler
	
	// Order domain handlers
	OrderHandler    *orderHandlers.OrderHandler
	ShippingHandler *orderHandlers.ShippingHandler
	
	// Shopping domain handlers
	CartHandler     *shoppingHandlers.CartHandler
	WishlistHandler *shoppingHandlers.WishlistHandler
	
	// Payment domain handlers
	PaymentHandler *paymentHandlers.PaymentHandler
	CouponHandler  *paymentHandlers.CouponHandler
	
	// Inventory domain handlers
	InventoryHandler *inventoryHandlers.InventoryHandler
	
	// Content domain handlers
	FileHandler *contentHandlers.FileHandler
	
	// Analytics domain handlers
	AnalyticsHandler *analyticsHandlers.AnalyticsHandler
	
	// Notification domain handlers
	NotificationHandler *notificationHandlers.NotificationHandler
	
	// Address domain handlers
	AddressHandler *addressHandlers.AddressHandler
}

// SetupRoutes sets up all domain routes
func SetupRoutes(router *gin.Engine, handlers *DomainHandlers) {
	// API version group
	v1 := router.Group("/api/v1")
	
	// Health check
	v1.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "E-commerce API is running",
			"version": "1.0.0",
		})
	})
	
	// Setup domain routes
	userRoutes.UserRoutes(v1, handlers.UserHandler, handlers.AdminHandler, handlers.OAuthHandler)
	productRoutes.ProductRoutes(v1, handlers.ProductHandler, handlers.CategoryHandler, handlers.ReviewHandler)
	orderRoutes.OrderRoutes(v1, handlers.OrderHandler, handlers.ShippingHandler)
	shoppingRoutes.ShoppingRoutes(v1, handlers.CartHandler, handlers.WishlistHandler)
	paymentRoutes.PaymentRoutes(v1, handlers.PaymentHandler, handlers.CouponHandler)
	inventoryRoutes.InventoryRoutes(v1, handlers.InventoryHandler)
	contentRoutes.ContentRoutes(v1, handlers.FileHandler)
	analyticsRoutes.AnalyticsRoutes(v1, handlers.AnalyticsHandler)
	notificationRoutes.NotificationRoutes(v1, handlers.NotificationHandler)
	addressRoutes.AddressRoutes(v1, handlers.AddressHandler)
}
