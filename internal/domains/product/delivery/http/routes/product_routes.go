package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/product/delivery/http/handlers"
	"github.com/bisosad1501/ecom-golang-clean-architecture/internal/delivery/http/middleware"
)

// ProductRoutes sets up product-related routes
func ProductRoutes(router *gin.RouterGroup, productHandler *handlers.ProductHandler, categoryHandler *handlers.CategoryHandler, reviewHandler *handlers.ReviewHandler) {
	// Public product routes
	products := router.Group("/products")
	{
		products.GET("", productHandler.GetProducts)
		products.GET("/:id", productHandler.GetProduct)
		products.GET("/search", productHandler.SearchProducts)
		products.GET("/featured", productHandler.GetFeaturedProducts)
		products.GET("/trending", productHandler.GetTrendingProducts)
		products.GET("/category/:categoryId", productHandler.GetProductsByCategory)
		products.GET("/:id/related", productHandler.GetRelatedProducts)
		products.GET("/:id/reviews", reviewHandler.GetProductReviews)
	}

	// Public category routes
	categories := router.Group("/categories")
	{
		categories.GET("", categoryHandler.GetCategories)
		categories.GET("/:id", categoryHandler.GetCategory)
		categories.GET("/:id/products", productHandler.GetProductsByCategory)
	}

	// Protected product routes (authentication required)
	protectedProducts := router.Group("/products")
	protectedProducts.Use(middleware.AuthMiddleware())
	{
		protectedProducts.POST("/:id/reviews", reviewHandler.CreateReview)
		protectedProducts.PUT("/reviews/:reviewId", reviewHandler.UpdateReview)
		protectedProducts.DELETE("/reviews/:reviewId", reviewHandler.DeleteReview)
		protectedProducts.POST("/reviews/:reviewId/vote", reviewHandler.VoteReview)
	}

	// Admin product routes
	adminProducts := router.Group("/admin/products")
	adminProducts.Use(middleware.AuthMiddleware(), middleware.AdminMiddleware())
	{
		adminProducts.POST("", productHandler.CreateProduct)
		adminProducts.PUT("/:id", productHandler.UpdateProduct)
		adminProducts.DELETE("/:id", productHandler.DeleteProduct)
		adminProducts.POST("/:id/images", productHandler.UploadProductImages)
		adminProducts.DELETE("/:id/images/:imageId", productHandler.DeleteProductImage)
		adminProducts.PUT("/:id/status", productHandler.UpdateProductStatus)
		adminProducts.GET("/stats", productHandler.GetProductStats)
	}

	// Admin category routes
	adminCategories := router.Group("/admin/categories")
	adminCategories.Use(middleware.AuthMiddleware(), middleware.AdminMiddleware())
	{
		adminCategories.POST("", categoryHandler.CreateCategory)
		adminCategories.PUT("/:id", categoryHandler.UpdateCategory)
		adminCategories.DELETE("/:id", categoryHandler.DeleteCategory)
		adminCategories.PUT("/:id/status", categoryHandler.UpdateCategoryStatus)
	}

	// Admin review routes
	adminReviews := router.Group("/admin/reviews")
	adminReviews.Use(middleware.AuthMiddleware(), middleware.AdminMiddleware())
	{
		adminReviews.GET("", reviewHandler.GetAllReviews)
		adminReviews.GET("/:id", reviewHandler.GetReview)
		adminReviews.PUT("/:id/moderate", reviewHandler.ModerateReview)
		adminReviews.DELETE("/:id", reviewHandler.DeleteReview)
		adminReviews.POST("/:id/reply", reviewHandler.ReplyToReview)
	}
}
