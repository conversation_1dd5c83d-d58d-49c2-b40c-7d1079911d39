package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/shopping/delivery/http/handlers"
	"github.com/bisosad1501/ecom-golang-clean-architecture/internal/delivery/http/middleware"
)

// ShoppingRoutes sets up shopping-related routes
func ShoppingRoutes(router *gin.RouterGroup, cartHandler *handlers.CartHandler, wishlistHandler *handlers.WishlistHandler) {
	// Cart routes (authentication required)
	cart := router.Group("/cart")
	cart.Use(middleware.AuthMiddleware())
	{
		cart.GET("", cartHandler.GetCart)
		cart.POST("/items", cartHandler.AddToCart)
		cart.PUT("/items/:itemId", cartHandler.UpdateCartItem)
		cart.DELETE("/items/:itemId", cartHandler.RemoveFromCart)
		cart.DELETE("", cartHandler.ClearCart)
		cart.GET("/summary", cartHandler.GetCartSummary)
		cart.POST("/validate", cartHandler.ValidateCart)
	}

	// Wishlist routes (authentication required)
	wishlist := router.Group("/wishlist")
	wishlist.Use(middleware.AuthMiddleware())
	{
		wishlist.GET("", wishlistHandler.GetWishlist)
		wishlist.POST("/items", wishlistHandler.AddToWishlist)
		wishlist.DELETE("/items/:productId", wishlistHandler.RemoveFromWishlist)
		wishlist.DELETE("", wishlistHandler.ClearWishlist)
		wishlist.POST("/items/:productId/move-to-cart", wishlistHandler.MoveToCart)
		wishlist.GET("/shared/:shareId", wishlistHandler.GetSharedWishlist)
		wishlist.POST("/share", wishlistHandler.ShareWishlist)
	}
}
