package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/user/delivery/http/handlers"
	"github.com/bisosad1501/ecom-golang-clean-architecture/internal/delivery/http/middleware"
)

// UserRoutes sets up user-related routes
func UserRoutes(router *gin.RouterGroup, userHandler *handlers.UserHandler, adminHandler *handlers.AdminHandler, oauthHandler *handlers.OAuthHandler) {
	// Public routes (no authentication required)
	public := router.Group("/users")
	{
		public.POST("/register", userHandler.Register)
		public.POST("/login", userHandler.Login)
		public.POST("/forgot-password", userHandler.ForgotPassword)
		public.POST("/reset-password", userHandler.ResetPassword)
		public.POST("/verify-email", userHandler.VerifyEmail)
		public.POST("/resend-verification", userHandler.ResendVerification)
	}

	// OAuth routes
	oauth := router.Group("/auth")
	{
		oauth.GET("/google", oauthHandler.GoogleLogin)
		oauth.GET("/google/callback", oauthHandler.GoogleCallback)
		oauth.GET("/facebook", oauthHandler.FacebookLogin)
		oauth.GET("/facebook/callback", oauthHandler.FacebookCallback)
	}

	// Protected routes (authentication required)
	protected := router.Group("/users")
	protected.Use(middleware.AuthMiddleware())
	{
		protected.GET("/profile", userHandler.GetProfile)
		protected.PUT("/profile", userHandler.UpdateProfile)
		protected.POST("/change-password", userHandler.ChangePassword)
		protected.POST("/logout", userHandler.Logout)
		protected.DELETE("/account", userHandler.DeleteAccount)
		
		// User preferences
		protected.GET("/preferences", userHandler.GetPreferences)
		protected.PUT("/preferences", userHandler.UpdatePreferences)
		
		// User activity
		protected.GET("/activity", userHandler.GetActivity)
		protected.GET("/sessions", userHandler.GetSessions)
		protected.DELETE("/sessions/:sessionId", userHandler.DeleteSession)
		
		// User verification
		protected.POST("/verify-phone", userHandler.VerifyPhone)
		protected.POST("/enable-2fa", userHandler.EnableTwoFactor)
		protected.POST("/disable-2fa", userHandler.DisableTwoFactor)
	}

	// Admin routes (admin authentication required)
	admin := router.Group("/admin/users")
	admin.Use(middleware.AuthMiddleware(), middleware.AdminMiddleware())
	{
		admin.GET("", adminHandler.GetUsers)
		admin.GET("/:id", adminHandler.GetUser)
		admin.PUT("/:id", adminHandler.UpdateUser)
		admin.DELETE("/:id", adminHandler.DeleteUser)
		admin.POST("/:id/activate", adminHandler.ActivateUser)
		admin.POST("/:id/deactivate", adminHandler.DeactivateUser)
		admin.POST("/:id/suspend", adminHandler.SuspendUser)
		admin.GET("/:id/activity", adminHandler.GetUserActivity)
		admin.GET("/:id/sessions", adminHandler.GetUserSessions)
		admin.DELETE("/:id/sessions", adminHandler.DeleteUserSessions)
		
		// User statistics
		admin.GET("/stats", adminHandler.GetUserStats)
		admin.GET("/:id/stats", adminHandler.GetUserDetailedStats)
		
		// Bulk operations
		admin.POST("/bulk/activate", adminHandler.BulkActivateUsers)
		admin.POST("/bulk/deactivate", adminHandler.BulkDeactivateUsers)
		admin.POST("/bulk/delete", adminHandler.BulkDeleteUsers)
	}
}
