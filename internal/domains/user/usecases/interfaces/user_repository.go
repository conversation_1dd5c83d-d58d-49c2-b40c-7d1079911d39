package interfaces

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/bisosad1501/ecom-golang-clean-architecture/internal/domains/user/entities"
	"github.com/bisosad1501/ecom-golang-clean-architecture/internal/shared/entities"
)

// UserRepository defines the interface for user data access
type UserRepository interface {
	// Create creates a new user
	Create(ctx context.Context, user *entities.User) error

	// GetByID retrieves a user by ID
	GetByID(ctx context.Context, id uuid.UUID) (*entities.User, error)

	// GetByEmail retrieves a user by email
	GetByEmail(ctx context.Context, email string) (*entities.User, error)

	// GetByUsername retrieves a user by username
	GetByUsername(ctx context.Context, username string) (*entities.User, error)

	// GetByGoogleID retrieves a user by Google ID
	GetByGoogleID(ctx context.Context, googleID string) (*entities.User, error)

	// GetByFacebookID retrieves a user by Facebook ID
	GetByFacebookID(ctx context.Context, facebookID string) (*entities.User, error)

	// Update updates an existing user
	Update(ctx context.Context, user *entities.User) error

	// Delete deletes a user by ID
	Delete(ctx context.Context, id uuid.UUID) error

	// List retrieves users with pagination
	List(ctx context.Context, pagination *entities.PaginationRequest) ([]*entities.User, *entities.PaginationResponse, error)

	// ExistsByEmail checks if a user exists with the given email
	ExistsByEmail(ctx context.Context, email string) (bool, error)

	// UpdatePassword updates user password
	UpdatePassword(ctx context.Context, userID uuid.UUID, hashedPassword string) error

	// SetActive sets user active status
	SetActive(ctx context.Context, userID uuid.UUID, active bool) error

	// UpdateLastLogin updates user's last login timestamp
	UpdateLastLogin(ctx context.Context, userID uuid.UUID) error

	// UpdateLastActivity updates user's last activity timestamp
	UpdateLastActivity(ctx context.Context, userID uuid.UUID) error

	// GetActiveUsers retrieves active users
	GetActiveUsers(ctx context.Context, pagination *entities.PaginationRequest) ([]*entities.User, *entities.PaginationResponse, error)

	// GetUsersByRole retrieves users by role
	GetUsersByRole(ctx context.Context, role entities.UserRole, pagination *entities.PaginationRequest) ([]*entities.User, *entities.PaginationResponse, error)

	// SearchUsers searches users by various criteria
	SearchUsers(ctx context.Context, query string, pagination *entities.PaginationRequest) ([]*entities.User, *entities.PaginationResponse, error)

	// GetUserStats retrieves user statistics
	GetUserStats(ctx context.Context, userID uuid.UUID) (*UserStats, error)

	// UpdateUserMetrics updates user metrics (orders, spent, etc.)
	UpdateUserMetrics(ctx context.Context, userID uuid.UUID, totalOrders int, totalSpent float64) error
}

// UserProfileRepository defines the interface for user profile data access
type UserProfileRepository interface {
	// Create creates a new user profile
	Create(ctx context.Context, profile *entities.UserProfile) error

	// GetByUserID retrieves a user profile by user ID
	GetByUserID(ctx context.Context, userID uuid.UUID) (*entities.UserProfile, error)

	// Update updates an existing user profile
	Update(ctx context.Context, profile *entities.UserProfile) error

	// Delete deletes a user profile by user ID
	Delete(ctx context.Context, userID uuid.UUID) error
}

// UserSessionRepository defines the interface for user session data access
type UserSessionRepository interface {
	// Create creates a new user session
	Create(ctx context.Context, session *entities.UserSession) error

	// GetByToken retrieves a session by token
	GetByToken(ctx context.Context, token string) (*entities.UserSession, error)

	// GetByUserID retrieves sessions by user ID
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.UserSession, error)

	// Update updates an existing session
	Update(ctx context.Context, session *entities.UserSession) error

	// Delete deletes a session by token
	Delete(ctx context.Context, token string) error

	// DeleteExpiredSessions deletes expired sessions
	DeleteExpiredSessions(ctx context.Context) error

	// DeleteUserSessions deletes all sessions for a user
	DeleteUserSessions(ctx context.Context, userID uuid.UUID) error
}

// UserVerificationRepository defines the interface for user verification data access
type UserVerificationRepository interface {
	// Create creates a new verification record
	Create(ctx context.Context, verification *entities.UserVerification) error

	// GetByToken retrieves a verification by token
	GetByToken(ctx context.Context, token string) (*entities.UserVerification, error)

	// GetByUserIDAndType retrieves verification by user ID and type
	GetByUserIDAndType(ctx context.Context, userID uuid.UUID, verificationType string) (*entities.UserVerification, error)

	// Update updates an existing verification
	Update(ctx context.Context, verification *entities.UserVerification) error

	// Delete deletes a verification by ID
	Delete(ctx context.Context, id uuid.UUID) error

	// DeleteExpiredVerifications deletes expired verifications
	DeleteExpiredVerifications(ctx context.Context) error
}

// UserStats represents user statistics
type UserStats struct {
	TotalOrders       int     `json:"total_orders"`
	TotalSpent        float64 `json:"total_spent"`
	AverageOrderValue float64 `json:"average_order_value"`
	LastOrderDate     *time.Time `json:"last_order_date"`
	FavoriteCategory  string  `json:"favorite_category"`
	LoyaltyPoints     int     `json:"loyalty_points"`
	MembershipTier    string  `json:"membership_tier"`
}
