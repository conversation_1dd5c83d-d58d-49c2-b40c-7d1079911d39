package entities

import (
	"time"

	"github.com/google/uuid"
)

// BaseEntity contains common fields for all entities
type BaseEntity struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// SoftDeleteEntity contains common fields for entities with soft delete
type SoftDeleteEntity struct {
	BaseEntity
	DeletedAt *time.Time `json:"deleted_at,omitempty" gorm:"index"`
}

// AuditableEntity contains common fields for entities that need audit trail
type AuditableEntity struct {
	BaseEntity
	CreatedBy *uuid.UUID `json:"created_by,omitempty" gorm:"type:uuid"`
	UpdatedBy *uuid.UUID `json:"updated_by,omitempty" gorm:"type:uuid"`
}
