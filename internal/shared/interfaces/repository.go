package interfaces

import (
	"context"

	"github.com/google/uuid"
	"github.com/bisosad1501/ecom-golang-clean-architecture/internal/shared/entities"
)

// BaseRepository defines common repository operations
type BaseRepository[T any] interface {
	Create(ctx context.Context, entity *T) error
	GetByID(ctx context.Context, id uuid.UUID) (*T, error)
	Update(ctx context.Context, entity *T) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, pagination *entities.PaginationRequest) ([]*T, *entities.PaginationResponse, error)
}

// CacheRepository defines caching operations
type CacheRepository interface {
	Set(ctx context.Context, key string, value interface{}, ttl int) error
	Get(ctx context.Context, key string, dest interface{}) error
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
	Clear(ctx context.Context, pattern string) error
}
