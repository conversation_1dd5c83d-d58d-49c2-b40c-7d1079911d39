package utils

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/bisosad1501/ecom-golang-clean-architecture/internal/shared/entities"
)

// SuccessResponse represents a successful API response
type SuccessResponse struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// ErrorResponse represents an error API response
type ErrorResponse struct {
	Error   string `json:"error"`
	Details string `json:"details,omitempty"`
}

// PaginatedResponse represents a paginated API response
type PaginatedResponse struct {
	Message    string                        `json:"message"`
	Data       interface{}                   `json:"data"`
	Pagination *entities.PaginationResponse `json:"pagination"`
}

// RespondWithSuccess sends a successful response
func RespondWithSuccess(c *gin.Context, statusCode int, message string, data interface{}) {
	c.JSON(statusCode, SuccessResponse{
		Message: message,
		Data:    data,
	})
}

// RespondWithError sends an error response
func RespondWithError(c *gin.Context, statusCode int, message string, details ...string) {
	response := ErrorResponse{
		Error: message,
	}
	if len(details) > 0 {
		response.Details = details[0]
	}
	c.JSON(statusCode, response)
}

// RespondWithPagination sends a paginated response
func RespondWithPagination(c *gin.Context, message string, data interface{}, pagination *entities.PaginationResponse) {
	c.JSON(http.StatusOK, PaginatedResponse{
		Message:    message,
		Data:       data,
		Pagination: pagination,
	})
}

// RespondWithValidationError sends a validation error response
func RespondWithValidationError(c *gin.Context, err error) {
	RespondWithError(c, http.StatusBadRequest, "Validation failed", err.Error())
}

// RespondWithNotFound sends a not found error response
func RespondWithNotFound(c *gin.Context, resource string) {
	RespondWithError(c, http.StatusNotFound, resource+" not found")
}

// RespondWithUnauthorized sends an unauthorized error response
func RespondWithUnauthorized(c *gin.Context) {
	RespondWithError(c, http.StatusUnauthorized, "Unauthorized")
}

// RespondWithForbidden sends a forbidden error response
func RespondWithForbidden(c *gin.Context) {
	RespondWithError(c, http.StatusForbidden, "Forbidden")
}

// RespondWithInternalError sends an internal server error response
func RespondWithInternalError(c *gin.Context, err error) {
	RespondWithError(c, http.StatusInternalServerError, "Internal server error", err.Error())
}
